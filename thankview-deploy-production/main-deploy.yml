---
# NEW PARALLEL APPROACH
- name: Deploy ThankView production Environment in Parallel
  hosts: localhost
  connection: local
  gather_facts: false
  vars:
    deploy_env: "production" # Can be overridden with -e deploy_env=prod
  tasks:
    - name: Display deployment start message
      debug:
        msg: "Starting ThankView parallel deployment to {{ deploy_env }} environment"

    - name: Run all deployments in parallel
      ansible.builtin.shell:
        cmd: |
          ansible-playbook -i aws_ec2.aws_ec2.yml webserver-deploy-main.yml &
          ansible-playbook -i aws_ec2.aws_ec2.yml media-deploy-main.yml &
          ansible-playbook -i aws_ec2.aws_ec2.yml secure-deploy-main.yml &
          ansible-playbook -i aws_ec2.aws_ec2.yml worker-deploy-main.yml &
          ansible-playbook -i aws_ec2.aws_ec2.yml other-servers-deploy-main.yml &
          wait
      args:
        chdir: "{{ playbook_dir }}"
      register: parallel_deploy_result
      failed_when: parallel_deploy_result.rc != 0

    - name: Display parallel deployment completion
      debug:
        msg: "All parallel deployments completed"


# main-deploy.yml - Main deployment playbook optimized for GitHub Actions

# COMMENTED OUT - ORIGINAL SEQUENTIAL APPROACH (in case we need to revert)
# - name: Deploy ThankView production Environment
#   hosts: localhost
#   connection: local
#   gather_facts: false
#   vars:
#     deploy_env: "production" # Can be overridden with -e deploy_env=prod
#   pre_tasks:
#     - name: Display deployment start message
#       debug:
#         msg: "Starting ThankView deployment to {{ deploy_env }} environment"
#
# - import_playbook: webserver-deploy-main.yml
#   tags: [web, webservers, all]
#   ignore_errors: yes # Continue with other deployments even if this one fails
#
# - import_playbook: media-deploy-main.yml
#   tags: [media, all]
#   ignore_errors: yes
#
# - import_playbook: secure-deploy-main.yml
#   tags: [secure, all]
#   ignore_errors: yes
#
# - import_playbook: worker-deploy-main.yml
#   tags: [workers, aws_workers, all]
#   ignore_errors: yes
#
# - import_playbook: other-servers-deploy-main.yml
#   tags: [others, sftp, schedule, all]
#   ignore_errors: yes
#
# - name: Send notification
#   hosts: localhost
#   connection: local
#   gather_facts: false
#   tags: [notification, always]
#   # tasks:
#   # - name: Retrieve Slack token from AWS Parameter Store
#   # amazon.aws.aws_ssm_parameter_store:
#   # names:
#   # - /thankview/slack-token
#   # region: us-east-1
#   # register: slack_params
#   # no_log: true
#   # - name: Send Slack notification
#   # community.general.slack:
#   # token: "{{ slack_params.parameters['/thankview/slack-token'] }}"
#   # msg: "{{ deploy_env | capitalize }} deployment completed via GitHub Actions!"
#   # channel: "#devalerts"
#   # failed_when: false # Ensure notification attempts don't cause the playbook to fail
