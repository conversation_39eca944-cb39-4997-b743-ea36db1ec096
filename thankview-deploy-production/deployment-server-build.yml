---
# deployment-server-build.yml - Build frontend assets on deployment server and distribute to web servers
- name: Build and distribute frontend assets
 hosts: prod_deployment
 gather_facts: false
 become: true
 become_user: ubuntu
 tasks:
   - name: Git operations
     ansible.builtin.shell:
       cmd: |
         GIT_SSH_COMMAND="ssh -i ~/.ssh/thankview_deploy -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git fetch
         GIT_SSH_COMMAND="ssh -i ~/.ssh/thankview_deploy -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git reset origin/production --hard
     args:
       chdir: /var/www/thank-views
   - name: Build assets
     ansible.builtin.shell:
       cmd: |
         npm install
         npm run prod
     args:
       chdir: /var/www/thank-views
   - name: Deploy assets
     ansible.builtin.shell:
       cmd: |
         export RSYNC_RSH="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
         alias rsync='rsync --no-group --no-owner'
         npm run deploy
     args:
       chdir: /var/www/thank-views
     async: 300
     poll: 5