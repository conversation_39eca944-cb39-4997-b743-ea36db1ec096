---
# deployment-server-build.yml - Build frontend assets on deployment server and distribute to web servers
- name: Build and distribute frontend assets
  hosts: prod_deployment
  gather_facts: false
  become: true
  become_user: ubuntu
  tasks:
    - name: Build and deploy frontend assets
      ansible.builtin.shell:
        cmd: |
          GIT_SSH_COMMAND="ssh -i ~/.ssh/thankview_deploy -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git fetch
          GIT_SSH_COMMAND="ssh -i ~/.ssh/thankview_deploy -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git reset origin/production --hard
          npm install
          npm run prod
          npm run deploy
      args:
        chdir: /var/www/thank-views
      environment:
        ANSIBLE_HOST_KEY_CHECKING: "False"