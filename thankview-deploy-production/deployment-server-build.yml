---
# deployment-server-build.yml - Build frontend assets on deployment server and distribute to web servers
- name: Add deployment server to inventory
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Add deployment server
      add_host:
        name: deployment-server
        ansible_host: ip-172-31-76-190.ec2.internal
        ansible_user: ubuntu
        ansible_ssh_private_key_file: /home/<USER>/.ssh/production_deploy_key

- name: Build and distribute frontend assets
  hosts: deployment-server
  gather_facts: false

  tasks:
    - name: Ensure .ssh directory exists
      ansible.builtin.file:
        path: ~/.ssh
        state: directory
        mode: '0700'

    - name: Get SSH key using AWS CLI
      ansible.builtin.shell: |
        aws ssm get-parameter --name "/thankview-prod/thankview_deploy" --with-decryption --region us-east-1 --query Parameter.Value --output text
      register: ssh_key_cli
      delegate_to: localhost
      no_log: true

    - name: Save SSH key from CLI to file
      ansible.builtin.copy:
        content: "{{ ssh_key_cli.stdout }}\n"
        dest: ~/.ssh/thankview_deploy
        mode: '0600'
      no_log: true

    - name: Configure SSH to skip host key checking for rsync
      ansible.builtin.shell: |
        echo "Host *" >> ~/.ssh/config
        echo "    StrictHostKeyChecking no" >> ~/.ssh/config
        echo "    UserKnownHostsFile /dev/null" >> ~/.ssh/config
      args:
        creates: ~/.ssh/config

    - name: Configure SSH to use the deploy key for GitHub
      ansible.builtin.blockinfile:
        path: ~/.ssh/config
        block: |
          Host github.com
              HostName github.com
              User git
              IdentityFile ~/.ssh/thankview_deploy
              StrictHostKeyChecking no
        marker: "# {mark} ANSIBLE MANAGED BLOCK - GitHub SSH Config"
        create: yes

    - name: Build and deploy frontend assets
      ansible.builtin.shell:
        cmd: |
          git fetch; git reset origin/production --hard
          npm install && npm run prod
          npm run deploy
      args:
        chdir: /var/www/thank-views