---
# deployment-server-build.yml - Build frontend assets on deployment server and distribute to web servers
- name: Build and distribute frontend assets
  hosts: prod_deployment
  gather_facts: false

  tasks:
    - name: Configure SSH to skip host key checking for rsync
      ansible.builtin.shell: |
        echo "Host *" >> ~/.ssh/config
        echo "    StrictHostKeyChecking no" >> ~/.ssh/config
        echo "    UserKnownHostsFile /dev/null" >> ~/.ssh/config
      args:
        creates: ~/.ssh/config
      become: true
      become_user: ubuntu

    - name: Debug environment and SSH
      ansible.builtin.shell:
        cmd: |
          echo "=== Current user ==="
          whoami
          echo "=== Current directory ==="
          pwd
          echo "=== SSH keys available ==="
          ls -la ~/.ssh/
          echo "=== SSH agent ==="
          echo $SSH_AUTH_SOCK
          ssh-add -l 2>/dev/null || echo "No SSH agent or no keys loaded"
          echo "=== Test GitHub SSH ==="
          ssh -T ************** 2>&1 || echo "SSH test failed"
      args:
        chdir: /var/www/thank-views
      become: true
      become_user: ubuntu
      register: debug_output

    - name: Show debug output
      debug:
        var: debug_output.stdout_lines

    - name: Build and deploy frontend assets
      ansible.builtin.shell:
        cmd: |
          git fetch; git reset origin/production --hard
          npm install && npm run prod
          npm run deploy
      args:
        chdir: /var/www/thank-views
      become: true
      become_user: ubuntu