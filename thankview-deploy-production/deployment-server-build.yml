---
# deployment-server-build.yml - Build frontend assets on deployment server and distribute to web servers
- name: Build and distribute frontend assets
  hosts: prod_deployment
  gather_facts: false

  tasks:
    - name: Configure SSH to skip host key checking for rsync
      ansible.builtin.shell: |
        echo "Host *" >> ~/.ssh/config
        echo "    StrictHostKeyChecking no" >> ~/.ssh/config
        echo "    UserKnownHostsFile /dev/null" >> ~/.ssh/config
      args:
        creates: ~/.ssh/config

    - name: Build and deploy frontend assets
      ansible.builtin.shell:
        cmd: |
          git fetch; git reset origin/production --hard
          npm install && npm run prod
          npm run deploy
      args:
        chdir: /var/www/thank-views