# Environment Configuration Reference

Quick reference for developers on where .env files are stored and which server groups use which secrets.

## AWS Secrets Manager Locations

### Web Servers
- **Secret**: `prod/webserver-env`
- **Server Group**: Web servers (`webserver-prod`)
- **Files Updated**: 
  - `/var/www/thank-views/.env`
  - `/var/www/ThankView-API/.env`
  - `/var/www/ThankView-Envelope-Builder/.env`
  - `/var/www/thank-views-ca/.env`

### Media Servers
- **Secrets**: 
  - `prod/media3.thankview.com-env` (for media3.thankview.com)
  - `prod/media4.thankview.com-env` (for media4.thankview.com)
  - `prod/media5.thankview.com-env` (for media5.thankview.com)
- **Server Group**: Media servers (`prod_media`)
- **Selection**: Automatically selected based on server hostname
- **Files Updated**: `/var/www/thank-views/.env`

### Secure Servers
- **Secrets**:
  - `prod/secure-us1-env` (US secure servers)
  - `prod/secure-ca1-env` (Canadian secure servers)
- **Server Group**: Secure servers (`prod_secure`)
- **Selection**: Based on hostname - servers with `secure-ca1` use CA secret, others use US secret
- **Files Updated**: `/var/www/thank-views/.env`

### API Servers
- **Secrets**:
  - `prod/api-host-env` (US API servers)
  - `prod/ca-api-host-env` (Canadian API servers)
- **Server Group**: API servers (`prod_api_host`)
- **Files Updated**: `/var/www/ThankView-API/.env`

### Worker Servers
- **AWS Parameter Store**: 
  - `/thankview-ps/prod/video-server` (video workers)
  - `/thankview-ps/prod/sends-server` (send servers)
- **Server Group**: AWS workers (`prod_aws_workers`)
- **Selection**: Based on server name pattern
- **Files Updated**: `/var/www/thank-views/.env`

### Other Servers (SFTP, Schedulers, Monitors)
- **Secrets**:
  - `prod/sftp-host-env` (SFTP servers)
  - `prod/scheduler-host-env` (US schedulers)
  - `prod/scheduler-host-ca-env` (CA schedulers)
  - `prod/send-scheduler-env` (US send schedulers)
  - `prod/send-scheduler-ca-env` (CA send schedulers)
  - `prod/monitor-env` (US monitors)
  - `prod/monitor-ca-env` (CA monitors)
- **Server Group**: Other servers (`prod_others`)
- **Files Updated**: `/var/www/thank-views/.env`

### Builder/Deployment Server
- **Secret**: `prod/builder-env`
- **Server Group**: Builder (`prod_deployment`)
- **Files Updated**: `/var/www/thank-views/.env`

## Quick Reference Table

| Server Type | AWS Secret/Parameter | Selection Logic |
|-------------|---------------------|-----------------|
| Web Servers | `prod/webserver-env` | All web servers |
| Media Servers | `prod/media[3,4,5].thankview.com-env` | By hostname |
| Secure US | `prod/secure-us1-env` | Default for secure servers |
| Secure CA | `prod/secure-ca1-env` | If hostname contains `secure-ca1` |
| API US | `prod/api-host-env` | Default for API servers |
| API CA | `prod/ca-api-host-env` | If in CA region |
| Video Workers | `/thankview-ps/prod/video-server` | If name contains `video-worker` |
| Send Servers | `/thankview-ps/prod/sends-server` | If name contains `sends-server` |
| SFTP US | `prod/sftp-host-env` | If name contains `sftp` |
| SFTP CA | `prod/ca-sftp-host-env` | If name contains `sftp` and in CA |
| Schedulers | `prod/scheduler-host[-ca]-env` | By region |
| Send Schedulers | `prod/send-scheduler[-ca]-env` | By region |
| Monitors | `prod/monitor[-ca]-env` | By region |
| Builder | `prod/builder-env` | Deployment server |

## How to Update Environment Variables

1. **Go to AWS Secrets Manager** (for most servers) or **Parameter Store** (for workers)
2. **Find the appropriate secret** using the table above
3. **Edit the secret** - secrets are stored in JSON format
4. **Run deployment** to apply changes to servers

## Notes
- All secrets are in JSON format in AWS Secrets Manager
- Parameter Store entries are encrypted SecureString format
- Deployment automatically selects the correct secret based on server hostname/tags
- Changes require redeployment to take effect on servers
