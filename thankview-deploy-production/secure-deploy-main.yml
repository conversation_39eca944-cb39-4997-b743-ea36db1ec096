---
# Secure-deploy-main.yml - Equivalent of the Envoy deploy-prod-secure task
- name: Deploy to secure servers
  hosts: prod_secure
  vars:
    app_path: /var/www/thank-views
    git_repo: "**************:evertrue/ThankView-App.git"  # Updated with correct repo
    git_branch: "{{ deploy_branch | default('production') }}"
    user: "www-data" # Based on the original PHP script verify if needs to be changed

  tasks:
    # - name: Ensure .ssh directory exists
    #   ansible.builtin.file:
    #     path: "/home/<USER>/.ssh"
    #     state: directory
    #     mode: '0700'
    #     owner: "{{ user }}"
    #     group: "{{ user }}"
    #   become: true

    # - name: Get SSH key using AWS CLI
    #   ansible.builtin.shell: |
    #     aws ssm get-parameter --name "/thankview-prod/thankview_deploy" --with-decryption --region us-east-1 --query Parameter.Value --output text
    #   register: ssh_key_cli
    #   delegate_to: localhost
    #   no_log: true

    # - name: Save SSH key from CLI to file
    #   ansible.builtin.copy:
    #     content: "{{ ssh_key_cli.stdout }}\n"
    #     dest: "/home/<USER>/.ssh/thankview_deploy"
    #     mode: '0600'
    #     owner: "{{ user }}"
    #     group: "{{ user }}"
    #   become: true
    #   no_log: true

    # - name: Install Python pip
    #   apt:
    #     name: python3-pip
    #     state: present
    #     update_cache: yes
    #   become: true

    # - name: Install required Python modules for AWS operations
    #   pip:
    #     name:
    #       - boto3
    #       - botocore
    #     state: present
    #     executable: pip3
    #     extra_args: "--system"  # Install system-wide
    #   become: true

    # - name: Fix MySQL repository GPG key
    #   apt_key:
    #     keyserver: keyserver.ubuntu.com
    #     id: B7B3B788A8D3785C
    #     state: present
    #   become: yes

    # - name: Update apt cache with release info changes
    #   shell: apt update --allow-releaseinfo-change
    #   environment:
    #     DEBIAN_FRONTEND: noninteractive
    #   become: yes

    # - name: Check if git repo already exists
    #   ansible.builtin.stat:
    #     path: "{{ app_path }}/.git"
    #   register: git_dir

    # - name: Ensure proper ownership of the Git repository
    #   ansible.builtin.file:
    #     path: "{{ app_path }}"
    #     state: directory
    #     recurse: yes
    #     owner: "{{ user }}"
    #     group: "{{ user }}"
    #   become: true

    # - name: Clone git repository if it doesn't exist
    #   ansible.builtin.git:
    #     repo: "{{ git_repo }}"
    #     dest: "{{ app_path }}"
    #     key_file: "/home/<USER>/.ssh/thankview_deploy"
    #     accept_hostkey: yes
    #     ssh_opts: "-o StrictHostKeyChecking=no"
    #   when: not git_dir.stat.exists
    #   become: true
    #   become_user: "{{ user }}"

    # - name: Reset repository to the latest commit
    #   ansible.builtin.shell:
    #     cmd: |
    #       git reset --hard HEAD
    #       git clean -fd
    #   args:
    #     chdir: "{{ app_path }}"
    #   become: true
    #   become_user: "{{ user }}"

    # - name: Pull latest code if repo exists
    #   ansible.builtin.git:
    #     repo: "{{ git_repo }}"
    #     dest: "{{ app_path }}"
    #     update: yes
    #     version: "{{ git_branch }}"
    #     key_file: "/home/<USER>/.ssh/thankview_deploy"
    #     accept_hostkey: yes
    #     ssh_opts: "-o StrictHostKeyChecking=no"
    #     force: yes  # Add this line to force the checkout not in stage playbook
    #   when: git_dir.stat.exists
    #   become: true
    #   become_user: "{{ user }}"

    # - name: Run composer update
    #   community.general.composer:
    #     command: update
    #     working_dir: "{{ app_path }}"
    #   environment:
    #     COMPOSER_DISCARD_CHANGES: "true"
    #   become: true
    #   become_user: "{{ user }}"
    #   register: composer_result
    #   failed_when:
    #     - composer_result.failed == true
    #     - "'ddtrace.so' not in composer_result.msg"
    #     - "'Script php artisan package:discover handling the post-autoload-dump event returned with error code 1' not in composer_result.msg"

    # - name: Ensure all dependencies are installed
    #   ansible.builtin.shell:
    #     cmd: composer install --optimize-autoloader --no-cache
    #   args:
    #     chdir: "{{ app_path }}"
    #   become: true
    #   become_user: "{{ user }}"

    # - name: Save git commit hash
    #   ansible.builtin.shell: git log -1 --pretty=%h > git_commit
    #   args:
    #     chdir: "{{ app_path }}"
    #   become: true
    #   become_user: "{{ user }}"

    # - name: Save git commit hash
    #   ansible.builtin.shell: git log -1 --pretty=%h > git_commit
    #   args:
    #     chdir: "{{ app_path }}"
    #   become: true
    #   become_user: "{{ user }}"

    # - name: Create .profile file for www-data if it doesn't exist
    #   ansible.builtin.file:
    #     path: "/home/<USER>/.profile"
    #     state: touch
    #     owner: "{{ user }}"
    #     group: "{{ user }}"
    #     mode: '0644'
    #   become: true

    - name: Run composer dump-autoload
      community.general.composer:
        command: composer dump-autoload --optimize
        working_dir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"
      register: composer_result
      failed_when:
        - composer_result.failed == true
        - "'ddtrace.so' not in composer_result.msg"
        - "'Script php artisan package:discover handling the post-autoload-dump event returned with error code 1' not in composer_result.msg"


    - name: Clear Laravel caches
      ansible.builtin.shell: "php artisan {{ item }}"
      args:
        chdir: "{{ app_path }}"
      loop:
        - "config:clear"
        - "route:clear"
        - "view:clear"
      become: true
      become_user: "{{ user }}"

    - name: Restart queue workers
      ansible.builtin.shell: php artisan queue:restart
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    # New task to fetch and update .env files from AWS Secrets Manager
    - name: Overwrite .env files from Secrets Manager via lookup
      block:
        - name: Gather EC2 metadata facts
          ec2_metadata_facts:
          register: ec2_metadata

        - name: Get instance ID from metadata
          set_fact:
            instance_id: "{{ ec2_metadata.ansible_facts.ansible_ec2_instance_id }}"

        - name: Get EC2 instance info to determine server name
          ec2_instance_info:
            instance_ids: "{{ instance_id }}"
            region: "{{ ec2_metadata.ansible_facts.ansible_ec2_placement_region }}"
          register: server_info
          delegate_to: localhost
          become: false

        - name: Extract server name from EC2 Name tag
          set_fact:
            server_name: "{{ item.value }}"
          loop: "{{ server_info.instances[0].tags | dict2items }}"
          when: item.key == 'Name'

        - name: Determine which secret to use based on server name
          set_fact:
            secret_name: "{{ 'prod/secure-ca1-env' if 'secure-ca1' in server_name else 'prod/secure-us1-env' }}"

        - name: Debug which secret will be used
          debug:
            msg: "Server name '{{ server_name }}' will use secret: {{ secret_name }}"

        - name: Fetch the full .env blob from Secrets Manager
          set_fact:
            env_json: "{{ lookup(
              'aws_secret',
              secret_name,
              region='us-east-1'
              ) }}"

        - name: Convert JSON to .env format
          set_fact:
            env_content: |
              {% for key, value in env_json.items() %}
              {{ key }}={{ value }}
              {% endfor %}

        - name: Fetch the API-specific .env blob from Secrets Manager (using same secret)
          set_fact:
            api_env_json: "{{ lookup(
              'aws_secret',
              secret_name,
              region='us-east-1'
              ) }}"

        - name: Convert API JSON to .env format
          set_fact:
            api_env_content: |
              {% for key, value in api_env_json.items() %}
              {{ key }}={{ value }}
              {% endfor %}

        - name: Define list of .env paths
          set_fact:
            env_file_paths:
              - /var/www/thank-views/.env
              - /var/www/ThankView-API/.env
              - /var/www/ThankView-Envelope-Builder/.env
              - /var/www/thank-views-ca/.env

        - name: Stat parent directories of each .env file
          stat:
            path: "{{ item | dirname }}"
          loop: "{{ env_file_paths }}"
          register: env_stats
          loop_control:
            label: "{{ item }}"
          ignore_errors: yes

        - name: Overwrite existing .env files with the SSM blob
          copy:
            dest: "{{ item.item }}"
            content: "{% if item.item == '/var/www/ThankView-API/.env' %}{{ api_env_content }}{% else %}{{ env_content }}{% endif %}"
            owner: www-data
            group: www-data
            mode: '0644'
          loop: "{{ env_stats.results }}"
          when: item.stat.exists and item.stat.isdir
          loop_control:
            label: "{{ item.item }}"
      become: true

    - name: Always restart PHP-FPM
      ansible.builtin.service:
        name: php8.1-fpm
        state: restarted
      become: true