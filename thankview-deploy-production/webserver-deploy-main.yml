- name: Deploy to web servers
  hosts: webserver_prod #webservers
  vars:
    app_path: /var/www/thank-views
    git_repo: "**************:evertrue/ThankView-App.git"
    git_branch: "{{ deploy_branch | default('production') }}"

  tasks:
    - name: Ensure www-data home directory exists
      ansible.builtin.file:
        path: /home/<USER>
        state: directory
        mode: '0700'
        owner: www-data
        group: www-data
      become: true

    - name: Ensure .ssh directory exists for www-data
      ansible.builtin.file:
        path: /home/<USER>/.ssh
        state: directory
        mode: '0700'
        owner: www-data
        group: www-data
      become: true

    - name: Get SSH key using AWS CLI
      ansible.builtin.shell: |
        aws ssm get-parameter --name "/thankview-prod/thankview_deploy" --with-decryption --region us-east-1 --query Parameter.Value --output text
      register: ssh_key_cli
      delegate_to: localhost
      no_log: true

    - name: Save SSH key from CLI to file
      ansible.builtin.copy:
        content: "{{ ssh_key_cli.stdout }}\n"
        dest: /home/<USER>/.ssh/thankview_deploy
        mode: '0600'
        owner: www-data
        group: www-data
      become: true
      no_log: true

    - name: Ensure correct permissions for SSH key
      ansible.builtin.file:
        path: /home/<USER>/.ssh/thankview_deploy
        state: file
        mode: '0600'
        owner: www-data
        group: www-data
      become: true

    - name: Change ownership of the application directory
      ansible.builtin.file:
        path: "{{ app_path }}"
        state: directory
        recurse: true
        owner: www-data
        group: www-data
        mode: '0775'
      become: true

    - name: Install Python pip and development tools
      apt:
        name:
          - python3-pip
          - python3-dev
          - python3-setuptools
        state: present
        update_cache: yes
      become: true

    - name: Upgrade pip to latest version
      pip:
        name: pip
        state: latest
        executable: pip3
      become: true

    - name: Install required Python modules for AWS operations
      pip:
        name:
          - boto3
          - botocore
        state: present
        executable: pip3
        extra_args: "--break-system-packages"
      become: true

    #This will reset repo and drop any local changes before pulling latest repo commit
    - name: Reset repository to the latest commit
      ansible.builtin.shell:
        cmd: |
          git reset --hard HEAD
          git clean -fd
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: www-data

    - name: Allow Git to work in this repository
      ansible.builtin.shell:
        cmd: git config --global --add safe.directory /var/www/thank-views
      become: true
      become_user: www-data

    - name: Pull latest code
      ansible.builtin.git:
        repo: "{{ git_repo }}"
        dest: "{{ app_path }}"
        update: yes
        version: "{{ git_branch }}"
        key_file: /home/<USER>/.ssh/thankview_deploy
        accept_hostkey: yes
        ssh_opts: "-o StrictHostKeyChecking=no"
        force: yes # Add this line to force the checkout not in stage playbook
      become: true
      become_user: www-data

    - name: Run composer update
      ansible.builtin.shell:
        cmd: composer update
      args:
        chdir: "{{ app_path }}"
      environment:
        COMPOSER_DISCARD_CHANGES: "true"
      become: true
      become_user: www-data
      register: composer_result
      failed_when:
        - composer_result.failed == true
        - "'ddtrace.so' not in composer_result.msg"
        - "'Script php artisan package:discover handling the post-autoload-dump event returned with error code 1' not in composer_result.msg"


    - name: Ensure all dependencies are installed
      ansible.builtin.shell:
        cmd: composer install --optimize-autoloader --no-cache
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: www-data

    - name: Save git commit hash
      ansible.builtin.shell: git log -1 --pretty=%h > git_commit
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: www-data

    - name: Run composer dump-autoload
      ansible.builtin.shell: composer dump-autoload
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: www-data
      environment:
        COMPOSER_DISCARD_CHANGES: "true"

    - name: Fix ownership of build directory
      ansible.builtin.shell:
        cmd: sudo chown -R ubuntu:ubuntu /var/www/thank-views/public/build/

    - name: Clear Laravel caches
      ansible.builtin.shell: "php artisan {{ item }}"
      args:
        chdir: "{{ app_path }}"
      loop:
        - "config:clear"
        - "route:clear"
        - "view:clear"
        - "static:clear"
      become: true
      become_user: www-data

    # New task to fetch and update .env files from AWS Secrets Manager
    - name: Overwrite .env files from Secrets Manager via lookup
      become: true
      block:
        - name: Fetch the full .env blob from Secrets Manager
          set_fact:
            env_json: "{{ lookup(
              'aws_secret',
              'prod/webserver-env',
              region='us-east-1'
              ) }}"

        - name: Convert JSON to .env format
          set_fact:
            env_content: |
              {% for key, value in env_json.items() %}
              {{ key }}={{ value }}
              {% endfor %}

        - name: Fetch the API-specific .env blob from Secrets Manager (using same secret for now)
          set_fact:
            api_env_json: "{{ lookup(
              'aws_secret',
              'prod/webserver-env',
              region='us-east-1'
              ) }}"

        - name: Convert API JSON to .env format
          set_fact:
            api_env_content: |
              {% for key, value in api_env_json.items() %}
              {{ key }}={{ value }}
              {% endfor %}

        - name: Define list of .env paths
          set_fact:
            env_file_paths:
              - /var/www/thank-views/.env
              - /var/www/ThankView-API/.env
              - /var/www/ThankView-Envelope-Builder/.env
              - /var/www/thank-views-ca/.env

        - name: Stat parent directories of each .env file
          stat:
            path: "{{ item | dirname }}"
          loop: "{{ env_file_paths }}"
          register: env_stats
          loop_control:
            label: "{{ item }}"
          ignore_errors: yes

        - name: Overwrite existing .env files with the SSM blob
          copy:
            dest: "{{ item.item }}"
            content: "{% if item.item == '/var/www/ThankView-API/.env' %}{{ api_env_content }}{% else %}{{ env_content }}{% endif %}"
            owner: www-data
            group: www-data
            mode: '0644'
            force: yes
          loop: "{{ env_stats.results }}"
          when: item.stat.exists and item.stat.isdir
          loop_control:
            label: "{{ item.item }}"

    - name: Always restart PHP-FPM
      ansible.builtin.service:
        name: php8.1-fpm
        state: restarted
      become: true

- import_playbook: deployment-server-build.yml