# ThankView Production Deployment Guide

This guide explains how to deploy the ThankView application to the production environment and provides details about the deployment workflow, server architecture, and troubleshooting steps.

## Table of Contents
- [Quick Start: How to Deploy](#quick-start-how-to-deploy)
- [Deployment Workflow Overview](#deployment-workflow-overview)
- [Deployment Workflow Diagram](#deployment-workflow-diagram)
- [Server Architecture](#server-architecture)
- [Deployment Playbooks](#deployment-playbooks)
- [Environment Configuration](#environment-configuration)
- [Troubleshooting](#troubleshooting)
- [Monitoring Deployments](#monitoring-deployments)

## Quick Start: How to Deploy

### Deploy via GitHub Actions UI
1. Go to the ThankView-App GitHub repository
2. Navigate to the "Actions" tab
3. Select the "Deploy ThankView App Production" workflow
4. Click "Run workflow" button
5. Configure the deployment:
   - **Environment**: Always use `production` (default)
   - **Deploy Target**: Select the server group to deploy to:
     - `all`: Deploy to all server groups (runs in parallel for faster deployment)
     - `web`: Deploy to web servers only
     - `media`: Deploy to media servers only
     - `secure`: Deploy to secure servers only
     - `aws-workers`: Deploy to AWS worker servers only
     - `others`: Deploy to other servers (SFTP, scheduler)
   - **Git Branch**: Enter the branch to deploy from (default: `production`)
6. Click "Run workflow" to start the deployment

> **⚠️ Production Deployment Warning**: Production deployments require extra caution. Always test changes in staging first and ensure you have proper backups before deploying to production.

## Deployment Workflow Overview

The production deployment process follows these steps:

### Single Server Group Deployment
When deploying to individual server groups (web, media, secure, etc.), the process runs sequentially.

### All Servers Deployment (Parallel)
When selecting "Deploy - All Servers", all server groups deploy simultaneously in parallel for faster deployment.

**Common deployment steps for all server types:**
1. **Inventory Discovery**: AWS EC2 dynamic inventory identifies all production servers based on tags
2. **SSH Key Retrieval**: Deployment SSH keys are retrieved from AWS Systems Manager Parameter Store
3. **Code Deployment**: The specified Git branch is pulled to each server
4. **Dependency Installation**: Composer dependencies are updated and installed
5. **Environment Configuration**: Environment variables are updated from AWS Secrets Manager
6. **APP_ROLE Configuration**: Server-specific APP_ROLE values are set based on EC2 instance tags
7. **Cache Clearing**: Laravel caches are cleared
8. **Service Restart**: PHP-FPM is restarted to apply changes

**Additional steps for web servers:**
- **Frontend Build**: NPM packages are installed and frontend assets are built and deployed via deployment server

## Deployment Workflow Diagram

```
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│                     │     │                     │     │                     │
│  GitHub Actions UI  │────▶│  GitHub Workflow    │────▶│  Ansible Playbooks  │
│  Manual Trigger     │     │  deploy.yaml        │     │  main-deploy.yml    │
│                     │     │                     │     │                     │
└─────────────────────┘     └─────────────────────┘     └──────────┬──────────┘
                                                                   │
                                                                   ▼
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│                     │     │                     │     │                     │
│  Service Restart    │◀────│  Update Env Vars    │◀────│  AWS EC2 Inventory  │
│  PHP-FPM            │     │  from AWS Secrets   │     │  Discovery          │
│                     │     │                     │     │                     │
└─────────────────────┘     └─────────────────────┘     └──────────┬──────────┘
       ▲                                                           │
       │                                                           ▼
┌──────┴──────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│                     │     │                     │     │                     │
│  Laravel Cache      │◀────│  Frontend Build     │◀────│  SSH Key Retrieval  │
│  Clear              │     │  NPM & Assets       │     │  from AWS SSM       │
│                     │     │                     │     │                     │
└─────────────────────┘     └──────────┬──────────┘     └──────────┬──────────┘
                                       │                            │
                                       │                            ▼
                                       │                  ┌─────────────────────┐
                                       │                  │                     │
                                       └─────────────────▶│  Code Deployment    │
                                                         │  & Dependencies      │
                                                         │                     │
                                                         └─────────────────────┘
```

## Server Architecture

The ThankView production environment consists of several server groups, each with specific roles:

| Server Group | Description | Ansible Group | Environment Tag |
|--------------|-------------|---------------|-----------------|
| Web Servers | Main application servers | `prod_webserver` | `prod` |
| Media Servers | Handle media processing | `prod_media` | `prod` |
| Secure Servers | Handle secure operations | `prod_secure` | `prod` |
| API Servers | API endpoints | `prod_api_host` | `prod` |
| AWS Workers | Process video and send tasks | `prod_aws_workers` | `prod` |
| Other Servers | SFTP and scheduler servers | `prod_others` | `prod` |
| CA Servers | Canadian servers | `prod_ca` | `prod` |

### Server Identification
Servers are identified using AWS tags, particularly:
- **Environment**: `prod` - Identifies production environment servers
- **Name** - Identifies the server role (e.g., `webserver-prod`, `secure-ca1`)
- **APP_ROLE** - Specific application role used in configuration
- **APP_MEDIA_HOST** - Media host configuration (for media servers)

## Deployment Playbooks

The deployment is organized into several playbooks:

- **main-deploy.yml**: Main orchestration playbook that runs all other playbooks in parallel for faster deployment
- **webserver-deploy-main.yml**: Deploys to web servers (includes frontend build via deployment server)
- **media-deploy-main.yml**: Deploys to media servers (includes APP_MEDIA_HOST configuration)
- **secure-deploy-main.yml**: Deploys to secure servers (conditional secrets based on server name)
- **worker-deploy-main.yml**: Deploys to worker servers
- **other-servers-deploy-main.yml**: Deploys to SFTP and scheduler servers
- **deployment-server-build.yml**: Handles frontend asset building and deployment to web servers

## Environment Configuration

### AWS Secrets Manager
Environment variables are stored in AWS Secrets Manager and retrieved during deployment:

- **prod/thankview-app**: Main application .env file (JSON format)
- **prod/webserver-env**: Web server specific .env file (JSON format)
- **prod/secure-us1-env**: Secure servers in US region (JSON format)
- **prod/secure-ca1-env**: Secure servers in CA region (JSON format)

### AWS Systems Manager Parameter Store
SSH deployment keys are stored in Parameter Store:

- **/thankview-prod/thankview_deploy**: SSH private key for Git repository access

### Environment Files Updated
The deployment process automatically updates the following .env files:
- `/var/www/thank-views/.env`
- `/var/www/ThankView-API/.env`
- `/var/www/ThankView-Envelope-Builder/.env`
- `/var/www/thank-views-ca/.env`

### Special Configuration Logic

#### Secure Servers
Secure servers use conditional secret selection:
- Servers with `secure-ca1` in hostname → Use `prod/secure-ca1-env`
- Other secure servers → Use `prod/secure-us1-env`

#### Media Servers
Media servers automatically extract and set `APP_MEDIA_HOST` from EC2 instance tags.

## Performance Improvements

### Parallel Deployment
The "Deploy - All Servers" option now runs all server group deployments in parallel, significantly reducing total deployment time. Instead of waiting for each server group to complete sequentially, all deployments run simultaneously.

### Deployment Server Architecture
Web server deployments use a dedicated deployment server to build frontend assets, which are then distributed to all web servers. This approach:
- Centralizes the build process
- Reduces load on individual web servers
- Ensures consistent asset builds across all servers

## Troubleshooting

### Common Issues and Solutions

#### Deployment Fails with SSH Errors
- Check that the SSH key in Parameter Store (`/thankview-prod/thankview_deploy`) is valid
- Ensure the GitHub repository is accessible with the deployment key
- Verify SSH key permissions on target servers

#### AWS Secrets Manager Access Issues
- Verify AWS credentials have proper permissions for Secrets Manager
- Check that secrets exist in the correct region (us-east-1)
- Ensure secret names match exactly (case-sensitive)

#### Composer or NPM Failures
- Check server disk space: `df -h`
- Check memory usage: `free -m`
- Try running the deployment with just the affected server group

#### Database Migration Failures
- Check database connectivity from the web servers
- Review Laravel migration logs: `/var/www/thank-views/storage/logs/laravel.log`
- Ensure database credentials are correct in secrets

#### Environment Variable Issues
- Verify Secrets Manager values in AWS Console
- Check permissions on .env files: `ls -la /var/www/thank-views/.env`
- Verify JSON format in secrets is correct

### Checking Deployment Status
To check the status of a deployment:

1. **GitHub Actions**: View the workflow run in the Actions tab
2. **Server Verification**:
   ```bash
   # Check application version
   ssh ubuntu@<server-ip> "cd /var/www/thank-views && git log -1 --pretty=%h"
   
   # Check PHP-FPM status
   ssh ubuntu@<server-ip> "sudo systemctl status php8.1-fpm"
   
   # Check .env file format
   ssh ubuntu@<server-ip> "head -5 /var/www/thank-views/.env"
   ```

## Monitoring Deployments

After deployment, monitor the application through:

### Application Logs
```bash
ssh ubuntu@<server-ip> "tail -f /var/www/thank-views/storage/logs/laravel.log"
```

### PHP-FPM Logs
```bash
ssh ubuntu@<server-ip> "sudo tail -f /var/log/php8.1-fpm.log"
```

### Web Server Access/Error Logs
```bash
ssh ubuntu@<server-ip> "sudo tail -f /var/log/nginx/access.log"
ssh ubuntu@<server-ip> "sudo tail -f /var/log/nginx/error.log"
```

### Application Health Check
- Access the production application in your browser
- Check API endpoints for proper responses
- Verify media processing functionality
- Test secure operations

---

> **📝 Note**: This README will be updated as the production deployment system is completed and moves from testing (`prod-test`) to full production (`prod`) environment.
